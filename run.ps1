# Knowledge Base Vector Store Run Script

Write-Host "Starting Knowledge Base Vector Store..." -ForegroundColor Cyan

# Check if Python is installed
if (-not (Get-Command python -ErrorAction SilentlyContinue)) {
    Write-Host "Python is not installed or not in PATH." -ForegroundColor Red
    Write-Host "Please install Python 3.10 or later." -ForegroundColor Red
    exit 1
}

# Check if Docker is installed
if (-not (Get-Command docker -ErrorAction SilentlyContinue)) {
    Write-Host "Docker is not installed or not in PATH." -ForegroundColor Red
    Write-Host "Please install Docker Desktop." -ForegroundColor Red
    exit 1
}

# Create necessary directories
Write-Host "Creating necessary directories..." -ForegroundColor Cyan
mkdir -Force data, uploads | Out-Null

# Start the service with Docker Compose
Write-Host "Starting Knowledge Base service with Docker Compose..." -ForegroundColor Cyan
docker-compose up -d --build

# Wait for the service to start
Write-Host "Waiting for the service to start..." -ForegroundColor Cyan
Start-Sleep -Seconds 5

# Check if the service is running
$isRunning = docker ps --filter "name=knowledge_base" --format "{{.Names}}"
if ($isRunning) {
    Write-Host "Knowledge Base service is running!" -ForegroundColor Green
    Write-Host "Access the UI at: http://localhost:8021" -ForegroundColor Green
    Write-Host "Access the API at: http://localhost:8020" -ForegroundColor Green
    Write-Host "API Documentation: http://localhost:8020/docs" -ForegroundColor Green
} else {
    Write-Host "Knowledge Base service failed to start. Check logs with: docker-compose logs" -ForegroundColor Red
}

Write-Host "To stop the service, run: docker-compose down" -ForegroundColor Yellow
