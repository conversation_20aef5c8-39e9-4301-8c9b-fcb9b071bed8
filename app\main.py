"""
Main application module for the Knowledge Base service.
"""

import os
import async<PERSON>
import uvicorn
from fastapi import FastAPI, HTTPException, UploadFile, File, Form, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse, RedirectResponse
import chromadb
from chromadb.config import Settings
from loguru import logger
import threading
from dotenv import load_dotenv
from pathlib import Path
from typing import List, Optional, Dict, Any

# Import local modules
from app.document_processor import DocumentProcessor
from app.ui_server import start_ui_server

# Load environment variables
load_dotenv()

# Configure logging
logger.add(
    os.getenv("LOG_FILE", "./data/knowledge_base.log"),
    rotation="10 MB",
    retention="1 month",
    level=os.getenv("LOG_LEVEL", "INFO"),
)

# Initialize FastAPI app
app = FastAPI(
    title="Knowledge Base API",
    description="API for managing and querying the knowledge base",
    version="1.0.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize Chroma client
chroma_db_path = os.getenv("CHROMA_DB_PATH", "./data/chroma")
os.makedirs(chroma_db_path, exist_ok=True)

chroma_client = chromadb.PersistentClient(
    path=chroma_db_path,
    settings=Settings(
        anonymized_telemetry=False,
    )
)

# Create a default collection if it doesn't exist
try:
    collection = chroma_client.get_or_create_collection(
        name="documents",
        metadata={"hnsw:space": "cosine"}
    )
    logger.info(f"Collection 'documents' initialized with {collection.count()} documents")
except Exception as e:
    logger.error(f"Error initializing Chroma collection: {e}")
    collection = None

# Initialize document processor
document_processor = DocumentProcessor(
    chroma_client=chroma_client,
    upload_dir=os.getenv("UPLOAD_DIR", "./uploads")
)

# Create upload directory if it doesn't exist
upload_dir = Path(os.getenv("UPLOAD_DIR", "./uploads"))
upload_dir.mkdir(parents=True, exist_ok=True)

# API routes
@app.get("/", response_class=HTMLResponse)
async def root():
    """Redirect to the UI."""
    return RedirectResponse(url="/docs")

@app.get("/health", response_model=Dict[str, str])
async def health_check():
    """Health check endpoint."""
    try:
        # Check if Chroma is responsive
        if collection:
            count = collection.count()
            return {
                "status": "healthy",
                "chroma_status": "connected",
                "document_count": str(count),
            }
        else:
            return {
                "status": "degraded",
                "chroma_status": "disconnected",
                "document_count": "unknown",
            }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
        }

@app.post("/api/v1/documents/upload", response_model=Dict[str, Any])
async def upload_document(
    file: UploadFile = File(...),
    collection_name: str = Form("documents"),
    metadata: Optional[str] = Form(None),
):
    """
    Upload a document to the knowledge base.
    
    Args:
        file: The document file to upload
        collection_name: The name of the collection to add the document to
        metadata: Optional JSON metadata for the document
    
    Returns:
        Information about the processed document
    """
    try:
        result = await document_processor.process_document(
            file=file,
            collection_name=collection_name,
            metadata_str=metadata,
        )
        return {
            "status": "success",
            "message": f"Document '{file.filename}' processed successfully",
            "document_id": result["document_id"],
            "chunks": result["chunk_count"],
        }
    except Exception as e:
        logger.error(f"Error processing document: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/documents", response_model=Dict[str, Any])
async def list_documents(
    collection_name: str = Query("documents"),
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0),
):
    """
    List documents in the knowledge base.
    
    Args:
        collection_name: The name of the collection to list documents from
        limit: Maximum number of documents to return
        offset: Number of documents to skip
    
    Returns:
        List of documents
    """
    try:
        documents = document_processor.list_documents(
            collection_name=collection_name,
            limit=limit,
            offset=offset,
        )
        return {
            "status": "success",
            "count": len(documents),
            "documents": documents,
        }
    except Exception as e:
        logger.error(f"Error listing documents: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/v1/documents/{document_id}", response_model=Dict[str, Any])
async def delete_document(
    document_id: str,
    collection_name: str = Query("documents"),
):
    """
    Delete a document from the knowledge base.
    
    Args:
        document_id: The ID of the document to delete
        collection_name: The name of the collection containing the document
    
    Returns:
        Status of the deletion operation
    """
    try:
        document_processor.delete_document(
            document_id=document_id,
            collection_name=collection_name,
        )
        return {
            "status": "success",
            "message": f"Document '{document_id}' deleted successfully",
        }
    except Exception as e:
        logger.error(f"Error deleting document: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/query", response_model=Dict[str, Any])
async def query_knowledge_base(
    query: str = Form(...),
    collection_name: str = Form("documents"),
    n_results: int = Form(5),
    filter_metadata: Optional[str] = Form(None),
):
    """
    Query the knowledge base.
    
    Args:
        query: The query text
        collection_name: The name of the collection to query
        n_results: Maximum number of results to return
        filter_metadata: Optional JSON metadata filter
    
    Returns:
        Query results
    """
    try:
        results = document_processor.query(
            query_text=query,
            collection_name=collection_name,
            n_results=n_results,
            filter_metadata_str=filter_metadata,
        )
        return {
            "status": "success",
            "query": query,
            "results": results,
        }
    except Exception as e:
        logger.error(f"Error querying knowledge base: {e}")
        raise HTTPException(status_code=500, detail=str(e))

def start_ui_server_thread():
    """Start the UI server in a separate thread."""
    ui_host = os.getenv("UI_HOST", "0.0.0.0")
    ui_port = int(os.getenv("UI_PORT", 8001))
    
    # Start the UI server
    asyncio.run(start_ui_server(host=ui_host, port=ui_port))

def main():
    """Main entry point for the application."""
    # Start the UI server in a separate thread
    ui_thread = threading.Thread(target=start_ui_server_thread, daemon=True)
    ui_thread.start()
    
    # Start the API server
    api_host = os.getenv("API_HOST", "0.0.0.0")
    api_port = int(os.getenv("API_PORT", 8000))
    
    logger.info(f"Starting Knowledge Base API server on {api_host}:{api_port}")
    uvicorn.run(
        "app.main:app",
        host=api_host,
        port=api_port,
        reload=os.getenv("ENVIRONMENT") == "development",
    )

if __name__ == "__main__":
    main()
