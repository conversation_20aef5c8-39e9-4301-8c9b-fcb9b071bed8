#!/bin/bash
# Knowledge Base Vector Store Run Script

echo -e "\e[36mStarting Knowledge Base Vector Store...\e[0m"

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo -e "\e[31mPython is not installed or not in PATH.\e[0m"
    echo -e "\e[31mPlease install Python 3.10 or later.\e[0m"
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "\e[31mDocker is not installed or not in PATH.\e[0m"
    echo -e "\e[31mPlease install Docker.\e[0m"
    exit 1
fi

# Create necessary directories
echo -e "\e[36mCreating necessary directories...\e[0m"
mkdir -p data uploads

# Start the service with Docker Compose
echo -e "\e[36mStarting Knowledge Base service with Docker Compose...\e[0m"
docker-compose up -d --build

# Wait for the service to start
echo -e "\e[36mWaiting for the service to start...\e[0m"
sleep 5

# Check if the service is running
is_running=$(docker ps --filter "name=knowledge_base" --format "{{.Names}}")
if [ -n "$is_running" ]; then
    echo -e "\e[32mKnowledge Base service is running!\e[0m"
    echo -e "\e[32mAccess the UI at: http://localhost:8021\e[0m"
    echo -e "\e[32mAccess the API at: http://localhost:8020\e[0m"
    echo -e "\e[32mAPI Documentation: http://localhost:8020/docs\e[0m"
else
    echo -e "\e[31mKnowledge Base service failed to start. Check logs with: docker-compose logs\e[0m"
fi

echo -e "\e[33mTo stop the service, run: docker-compose down\e[0m"
