"""
Document processor module for the Knowledge Base service.
"""

import os
import json
import uuid
import shutil
from pathlib import Path
from typing import Dict, List, Any, Optional
import chromadb
from fastapi import UploadFile
from loguru import logger
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import (
    PyPDFLoader,
    UnstructuredMarkdownLoader,
    TextLoader,
    UnstructuredHTMLLoader,
)

class DocumentProcessor:
    """Document processor for the Knowledge Base service."""

    def __init__(self, chroma_client, upload_dir: str = "./uploads"):
        """
        Initialize the document processor.
        
        Args:
            chroma_client: ChromaDB client
            upload_dir: Directory to store uploaded files
        """
        self.chroma_client = chroma_client
        self.upload_dir = Path(upload_dir)
        self.upload_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize text splitter
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len,
        )

    async def process_document(
        self, 
        file: UploadFile, 
        collection_name: str = "documents",
        metadata_str: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process a document and add it to the knowledge base.
        
        Args:
            file: The document file to process
            collection_name: The name of the collection to add the document to
            metadata_str: Optional JSON metadata for the document
            
        Returns:
            Information about the processed document
        """
        # Generate a unique ID for the document
        document_id = str(uuid.uuid4())
        
        # Parse metadata if provided
        metadata = {}
        if metadata_str:
            try:
                metadata = json.loads(metadata_str)
            except json.JSONDecodeError:
                logger.warning(f"Invalid metadata JSON: {metadata_str}")
        
        # Add file information to metadata
        metadata["filename"] = file.filename
        metadata["content_type"] = file.content_type
        metadata["document_id"] = document_id
        
        # Save the file to the upload directory
        file_path = self.upload_dir / f"{document_id}_{file.filename}"
        with open(file_path, "wb") as f:
            shutil.copyfileobj(file.file, f)
        
        # Load and process the document based on file type
        try:
            chunks = self._load_and_split_document(file_path)
            
            # Get or create collection
            collection = self.chroma_client.get_or_create_collection(name=collection_name)
            
            # Add document chunks to the collection
            texts = [chunk.page_content for chunk in chunks]
            metadatas = [
                {
                    **chunk.metadata,
                    **metadata,
                    "chunk_id": i,
                }
                for i, chunk in enumerate(chunks)
            ]
            ids = [f"{document_id}_{i}" for i in range(len(chunks))]
            
            collection.add(
                documents=texts,
                metadatas=metadatas,
                ids=ids,
            )
            
            logger.info(f"Added document '{file.filename}' with ID {document_id} to collection '{collection_name}'")
            
            return {
                "document_id": document_id,
                "filename": file.filename,
                "chunk_count": len(chunks),
                "collection": collection_name,
            }
            
        except Exception as e:
            # Clean up on error
            if file_path.exists():
                file_path.unlink()
            logger.error(f"Error processing document: {e}")
            raise

    def _load_and_split_document(self, file_path: Path):
        """
        Load and split a document into chunks.
        
        Args:
            file_path: Path to the document file
            
        Returns:
            List of document chunks
        """
        # Determine the loader based on file extension
        file_extension = file_path.suffix.lower()
        
        if file_extension == ".pdf":
            loader = PyPDFLoader(str(file_path))
        elif file_extension in [".md", ".markdown"]:
            loader = UnstructuredMarkdownLoader(str(file_path))
        elif file_extension in [".txt", ".text"]:
            loader = TextLoader(str(file_path))
        elif file_extension in [".html", ".htm"]:
            loader = UnstructuredHTMLLoader(str(file_path))
        else:
            # Default to text loader
            loader = TextLoader(str(file_path))
        
        # Load the document
        documents = loader.load()
        
        # Split the document into chunks
        chunks = self.text_splitter.split_documents(documents)
        
        return chunks

    def list_documents(
        self, 
        collection_name: str = "documents",
        limit: int = 100,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        List documents in the knowledge base.
        
        Args:
            collection_name: The name of the collection to list documents from
            limit: Maximum number of documents to return
            offset: Number of documents to skip
            
        Returns:
            List of documents
        """
        collection = self.chroma_client.get_collection(name=collection_name)
        
        # Get all document IDs and metadata
        result = collection.get()
        
        # Group by document_id to get unique documents
        documents = {}
        for i, metadata in enumerate(result["metadatas"]):
            if "document_id" in metadata:
                doc_id = metadata["document_id"]
                if doc_id not in documents:
                    documents[doc_id] = {
                        "document_id": doc_id,
                        "filename": metadata.get("filename", "Unknown"),
                        "content_type": metadata.get("content_type", "Unknown"),
                        "chunk_count": 0,
                    }
                documents[doc_id]["chunk_count"] += 1
        
        # Convert to list and apply pagination
        document_list = list(documents.values())
        return document_list[offset:offset+limit]

    def delete_document(
        self, 
        document_id: str,
        collection_name: str = "documents"
    ) -> None:
        """
        Delete a document from the knowledge base.
        
        Args:
            document_id: The ID of the document to delete
            collection_name: The name of the collection containing the document
        """
        collection = self.chroma_client.get_collection(name=collection_name)
        
        # Delete document chunks from the collection
        collection.delete(
            where={"document_id": document_id}
        )
        
        # Delete the file from the upload directory
        for file_path in self.upload_dir.glob(f"{document_id}_*"):
            file_path.unlink()
            
        logger.info(f"Deleted document with ID {document_id} from collection '{collection_name}'")

    def query(
        self,
        query_text: str,
        collection_name: str = "documents",
        n_results: int = 5,
        filter_metadata_str: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Query the knowledge base.
        
        Args:
            query_text: The query text
            collection_name: The name of the collection to query
            n_results: Maximum number of results to return
            filter_metadata_str: Optional JSON metadata filter
            
        Returns:
            Query results
        """
        collection = self.chroma_client.get_collection(name=collection_name)
        
        # Parse filter metadata if provided
        where_filter = None
        if filter_metadata_str:
            try:
                where_filter = json.loads(filter_metadata_str)
            except json.JSONDecodeError:
                logger.warning(f"Invalid filter metadata JSON: {filter_metadata_str}")
        
        # Query the collection
        results = collection.query(
            query_texts=[query_text],
            n_results=n_results,
            where=where_filter,
        )
        
        # Format the results
        formatted_results = []
        for i in range(len(results["ids"][0])):
            formatted_results.append({
                "id": results["ids"][0][i],
                "text": results["documents"][0][i],
                "metadata": results["metadatas"][0][i],
                "distance": results["distances"][0][i] if "distances" in results else None,
            })
            
        return formatted_results
