services:
  knowledge-base-service:
    container_name: knowledge_base
    build: .
    ports:
      - "8020:8000"  # API port
      - "8021:8001"  # UI port
    volumes:
      - ./data:/app/data
      - ./uploads:/app/uploads
    env_file:
      - .env
    environment:
      - ENVIRONMENT=production
      - CHROMA_DB_PATH=/app/data/chroma
      - UPLOAD_DIR=/app/uploads
      - API_PORT=8000
      - UI_PORT=8001
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
