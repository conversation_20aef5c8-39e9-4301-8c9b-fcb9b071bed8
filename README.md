# Knowledge Base Vector Store

A document storage and retrieval system using ChromaDB for vector embeddings, allowing semantic search across PDF files and other documents.

## Features

- Upload and process PDF, text, Markdown, and HTML documents
- Store document content as vector embeddings in ChromaDB
- Search documents using natural language queries
- Web UI for document management and search
- REST API for programmatic access
- Docker containerization for easy deployment

## Directory Structure

```
VECTORS/
├── app/                  # Application code
│   ├── __init__.py
│   ├── main.py           # Main application entry point
│   ├── document_processor.py  # Document processing logic
│   └── ui_server.py      # UI server implementation
├── data/                 # Data storage
│   └── chroma/           # ChromaDB persistent storage
├── uploads/              # Document upload directory
├── ui/                   # UI files
│   ├── templates/        # Jinja2 templates
│   └── static/           # Static assets (CSS, JS)
├── Dockerfile            # Docker configuration
├── docker-compose.yml    # Docker Compose configuration
├── requirements.txt      # Python dependencies
└── .env                  # Environment variables
```

## Getting Started

### Prerequisites

- Docker and Docker Compose
- Python 3.10+ (for local development)

### Running with Docker

1. <PERSON>lone the repository
2. Navigate to the VECTORS directory
3. Run the following command:

```bash
docker-compose up -d
```

4. Access the UI at http://localhost:8021
5. Access the API at http://localhost:8020

### Local Development

1. Create a virtual environment:

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. Install dependencies:

```bash
pip install -r requirements.txt
```

3. Run the application:

```bash
python -m app.main
```

## API Endpoints

- `GET /health` - Health check endpoint
- `POST /api/v1/documents/upload` - Upload a document
- `GET /api/v1/documents` - List all documents
- `DELETE /api/v1/documents/{document_id}` - Delete a document
- `POST /api/v1/query` - Query the knowledge base

## Usage

### Uploading Documents

1. Navigate to the Upload page
2. Select a document file (PDF, TXT, MD, HTML)
3. Click Upload

### Searching

1. Navigate to the Search page
2. Enter a natural language query
3. View the results

## Accessing from Other Services

To query the knowledge base from other services, use the API endpoints. For example:

```python
import requests

# Query the knowledge base
response = requests.post(
    "http://knowledge_base:8000/api/v1/query",
    data={
        "query": "What are the SARS guidelines for CUSCAR?",
        "collection_name": "documents",
        "n_results": 5
    }
)

results = response.json()["results"]
```
