"""
Simplified main application module for the Knowledge Base service.
"""

import os
import json
import uuid
import shutil
from pathlib import Path
from typing import Dict, List, Any, Optional
import asyncio
import threading
import uvicorn
from fastapi import FastAPI, HTTPException, UploadFile, File, Form, Query, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, JSONResponse, RedirectResponse
from loguru import logger
from dotenv import load_dotenv
import pypdf

# Load environment variables
load_dotenv()

# Configure logging
log_file = os.getenv("LOG_FILE", "./data/knowledge_base.log")
os.makedirs(os.path.dirname(log_file), exist_ok=True)
logger.add(
    log_file,
    rotation="10 MB",
    retention="1 month",
    level=os.getenv("LOG_LEVEL", "INFO"),
)

# Initialize FastAPI app
app = FastAPI(
    title="Knowledge Base API",
    description="API for managing and querying the knowledge base",
    version="1.0.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Set up templates
templates_dir = Path(__file__).parent.parent / "ui" / "templates"
templates = Jinja2Templates(directory=str(templates_dir))

# Set up static files
static_dir = Path(__file__).parent.parent / "ui" / "static"
app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")

# Create upload directory if it doesn't exist
upload_dir = Path(os.getenv("UPLOAD_DIR", "./uploads"))
upload_dir.mkdir(parents=True, exist_ok=True)

# Create data directory if it doesn't exist
data_dir = Path(os.getenv("DATA_DIR", "./data"))
data_dir.mkdir(parents=True, exist_ok=True)

# Simple in-memory document store
documents = {}
document_content = {}

# API routes
@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """Render the index page."""
    return templates.TemplateResponse(
        "index.html",
        {"request": request, "title": "Knowledge Base"}
    )

@app.get("/health", response_model=Dict[str, str])
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "document_count": str(len(documents)),
    }

@app.post("/api/v1/documents/upload")
async def upload_document(
    request: Request,
    file: UploadFile = File(...),
    collection_name: str = Form("documents"),
    metadata: Optional[str] = Form(None),
):
    """Upload a document to the knowledge base."""
    try:
        # Generate a unique ID for the document
        document_id = str(uuid.uuid4())
        
        # Parse metadata if provided
        meta_data = {}
        if metadata:
            try:
                meta_data = json.loads(metadata)
            except json.JSONDecodeError:
                logger.warning(f"Invalid metadata JSON: {metadata}")
        
        # Add file information to metadata
        meta_data["filename"] = file.filename
        meta_data["content_type"] = file.content_type
        meta_data["document_id"] = document_id
        meta_data["collection"] = collection_name
        
        # Save the file to the upload directory
        file_path = upload_dir / f"{document_id}_{file.filename}"
        with open(file_path, "wb") as f:
            shutil.copyfileobj(file.file, f)
        
        # Extract text from the file based on its type
        content = ""
        if file.filename.lower().endswith(".pdf"):
            content = extract_text_from_pdf(file_path)
        else:
            # For other file types, just read as text
            with open(file_path, "rb") as f:
                content = f.read().decode("utf-8", errors="ignore")
        
        # Store the document metadata and content
        documents[document_id] = meta_data
        document_content[document_id] = content
        
        # Save documents to disk for persistence
        save_documents_to_disk()
        
        logger.info(f"Added document '{file.filename}' with ID {document_id}")
        
        if request.headers.get("accept") == "application/json":
            return {
                "status": "success",
                "message": f"Document '{file.filename}' processed successfully",
                "document_id": document_id,
            }
        else:
            return RedirectResponse(url="/documents", status_code=303)
            
    except Exception as e:
        logger.error(f"Error processing document: {e}")
        if request.headers.get("accept") == "application/json":
            raise HTTPException(status_code=500, detail=str(e))
        else:
            return templates.TemplateResponse(
                "error.html",
                {
                    "request": request,
                    "title": "Upload Error",
                    "error": str(e),
                }
            )

@app.get("/documents", response_class=HTMLResponse)
async def list_documents_page(request: Request):
    """Render the documents page."""
    return templates.TemplateResponse(
        "documents.html",
        {
            "request": request,
            "title": "Documents",
            "documents": list(documents.values()),
        }
    )

@app.get("/api/v1/documents")
async def list_documents(
    collection_name: str = Query("documents"),
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0),
):
    """List documents in the knowledge base."""
    try:
        # Filter by collection if specified
        filtered_docs = [
            doc for doc in documents.values()
            if doc.get("collection") == collection_name
        ]
        
        # Apply pagination
        paginated_docs = filtered_docs[offset:offset+limit]
        
        return {
            "status": "success",
            "count": len(paginated_docs),
            "documents": paginated_docs,
        }
    except Exception as e:
        logger.error(f"Error listing documents: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/documents/{document_id}/delete")
async def delete_document_ui(request: Request, document_id: str):
    """Handle document deletion from UI."""
    try:
        if document_id in documents:
            # Delete the document file
            filename = documents[document_id].get("filename", "")
            file_path = upload_dir / f"{document_id}_{filename}"
            if file_path.exists():
                file_path.unlink()
            
            # Remove from memory
            del documents[document_id]
            if document_id in document_content:
                del document_content[document_id]
            
            # Save changes to disk
            save_documents_to_disk()
            
            logger.info(f"Deleted document with ID {document_id}")
        
        return RedirectResponse(url="/documents", status_code=303)
    except Exception as e:
        logger.error(f"Error deleting document: {e}")
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "title": "Delete Error",
                "error": str(e),
            }
        )

@app.delete("/api/v1/documents/{document_id}")
async def delete_document(document_id: str):
    """Delete a document from the knowledge base."""
    try:
        if document_id in documents:
            # Delete the document file
            filename = documents[document_id].get("filename", "")
            file_path = upload_dir / f"{document_id}_{filename}"
            if file_path.exists():
                file_path.unlink()
            
            # Remove from memory
            del documents[document_id]
            if document_id in document_content:
                del document_content[document_id]
            
            # Save changes to disk
            save_documents_to_disk()
            
            logger.info(f"Deleted document with ID {document_id}")
            
            return {
                "status": "success",
                "message": f"Document '{document_id}' deleted successfully",
            }
        else:
            raise HTTPException(status_code=404, detail=f"Document '{document_id}' not found")
    except Exception as e:
        logger.error(f"Error deleting document: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/search", response_class=HTMLResponse)
async def search_page(request: Request):
    """Render the search page."""
    return templates.TemplateResponse(
        "search.html",
        {"request": request, "title": "Search Knowledge Base"}
    )

@app.post("/search", response_class=HTMLResponse)
async def search_ui(
    request: Request,
    query: str = Form(...),
    collection: str = Form("documents"),
    n_results: int = Form(5),
):
    """Handle search request from UI."""
    try:
        results = simple_search(query, collection, n_results)
        return templates.TemplateResponse(
            "search_results.html",
            {
                "request": request,
                "title": "Search Results",
                "query": query,
                "results": results,
            }
        )
    except Exception as e:
        logger.error(f"Error searching: {e}")
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "title": "Search Error",
                "error": str(e),
            }
        )

@app.post("/api/v1/query")
async def query_knowledge_base(
    query: str = Form(...),
    collection_name: str = Form("documents"),
    n_results: int = Form(5),
):
    """Query the knowledge base."""
    try:
        results = simple_search(query, collection_name, n_results)
        return {
            "status": "success",
            "query": query,
            "results": results,
        }
    except Exception as e:
        logger.error(f"Error querying knowledge base: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/upload", response_class=HTMLResponse)
async def upload_page(request: Request):
    """Render the upload page."""
    return templates.TemplateResponse(
        "upload.html",
        {"request": request, "title": "Upload Document"}
    )

# Helper functions
def extract_text_from_pdf(file_path):
    """Extract text from a PDF file."""
    try:
        text = ""
        with open(file_path, "rb") as f:
            pdf = pypdf.PdfReader(f)
            for page_num in range(len(pdf.pages)):
                page = pdf.pages[page_num]
                text += page.extract_text() + f"\n\n--- Page {page_num + 1} ---\n\n"
        return text
    except Exception as e:
        logger.error(f"Error extracting text from PDF: {e}")
        return f"Error extracting text: {str(e)}"

def simple_search(query, collection_name, n_results):
    """
    Simple search function that looks for query terms in document content.
    This is a very basic implementation without vector embeddings.
    """
    query = query.lower()
    results = []
    
    # Filter documents by collection
    collection_docs = {
        doc_id: doc for doc_id, doc in documents.items()
        if doc.get("collection") == collection_name
    }
    
    # Search for query in each document
    for doc_id, doc in collection_docs.items():
        if doc_id in document_content:
            content = document_content[doc_id].lower()
            
            # Simple relevance score based on number of query term occurrences
            relevance = content.count(query)
            
            if relevance > 0:
                # Find a snippet containing the query
                start_idx = content.find(query)
                if start_idx >= 0:
                    # Extract a snippet around the query
                    start = max(0, start_idx - 100)
                    end = min(len(content), start_idx + len(query) + 100)
                    snippet = content[start:end]
                    
                    results.append({
                        "id": doc_id,
                        "text": snippet,
                        "metadata": doc,
                        "distance": 1.0 - (relevance / 100),  # Lower is better
                    })
    
    # Sort by relevance (distance)
    results.sort(key=lambda x: x["distance"])
    
    # Return top N results
    return results[:n_results]

def save_documents_to_disk():
    """Save documents metadata and content to disk for persistence."""
    try:
        # Save metadata
        with open(data_dir / "documents.json", "w") as f:
            json.dump(documents, f)
        
        # Save content
        with open(data_dir / "content.json", "w") as f:
            json.dump(document_content, f)
            
        logger.info("Documents saved to disk")
    except Exception as e:
        logger.error(f"Error saving documents to disk: {e}")

def load_documents_from_disk():
    """Load documents metadata and content from disk."""
    global documents, document_content
    
    try:
        # Load metadata
        metadata_path = data_dir / "documents.json"
        if metadata_path.exists():
            with open(metadata_path, "r") as f:
                documents = json.load(f)
            
        # Load content
        content_path = data_dir / "content.json"
        if content_path.exists():
            with open(content_path, "r") as f:
                document_content = json.load(f)
                
        logger.info(f"Loaded {len(documents)} documents from disk")
    except Exception as e:
        logger.error(f"Error loading documents from disk: {e}")
        # Initialize empty if loading fails
        documents = {}
        document_content = {}

# Load documents on startup
load_documents_from_disk()

def main():
    """Main entry point for the application."""
    # Start the API server
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8020))
    
    logger.info(f"Starting Knowledge Base API server on {host}:{port}")
    uvicorn.run(
        "app.simplified_main:app",
        host=host,
        port=port,
        reload=os.getenv("ENVIRONMENT") == "development",
    )

if __name__ == "__main__":
    main()
