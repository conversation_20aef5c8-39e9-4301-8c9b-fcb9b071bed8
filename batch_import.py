#!/usr/bin/env python
"""
Batch import script for the Knowledge Base Vector Store.

This script allows you to import multiple documents from a directory into the knowledge base.
"""

import os
import sys
import argparse
import requests
from pathlib import Path
import mimetypes
import time
import json

def main():
    """Main entry point for the batch import script."""
    parser = argparse.ArgumentParser(description="Batch import documents into the Knowledge Base")
    parser.add_argument("directory", help="Directory containing documents to import")
    parser.add_argument("--api-url", default="http://localhost:8020", help="API URL (default: http://localhost:8020)")
    parser.add_argument("--collection", default="documents", help="Collection name (default: documents)")
    parser.add_argument("--metadata", help="JSON metadata to apply to all documents")
    parser.add_argument("--extensions", default=".pdf,.txt,.md,.html,.htm", help="Comma-separated list of file extensions to import")
    parser.add_argument("--recursive", action="store_true", help="Recursively search for files in subdirectories")
    
    args = parser.parse_args()
    
    # Validate directory
    directory = Path(args.directory)
    if not directory.exists() or not directory.is_dir():
        print(f"Error: Directory '{directory}' does not exist or is not a directory")
        return 1
    
    # Parse extensions
    extensions = [ext.strip() for ext in args.extensions.split(",")]
    
    # Find files
    files = []
    if args.recursive:
        for root, _, filenames in os.walk(directory):
            for filename in filenames:
                file_path = Path(root) / filename
                if any(file_path.suffix.lower() == ext.lower() for ext in extensions):
                    files.append(file_path)
    else:
        for ext in extensions:
            files.extend(directory.glob(f"*{ext}"))
    
    if not files:
        print(f"No files found with extensions {extensions} in {directory}")
        return 0
    
    print(f"Found {len(files)} files to import")
    
    # Import files
    success_count = 0
    error_count = 0
    
    for file_path in files:
        print(f"Importing {file_path}... ", end="", flush=True)
        
        try:
            # Determine content type
            content_type, _ = mimetypes.guess_type(str(file_path))
            if not content_type:
                if file_path.suffix.lower() == ".pdf":
                    content_type = "application/pdf"
                elif file_path.suffix.lower() in [".txt", ".text"]:
                    content_type = "text/plain"
                elif file_path.suffix.lower() in [".md", ".markdown"]:
                    content_type = "text/markdown"
                elif file_path.suffix.lower() in [".html", ".htm"]:
                    content_type = "text/html"
                else:
                    content_type = "application/octet-stream"
            
            # Prepare request
            url = f"{args.api_url}/api/v1/documents/upload"
            files = {
                "file": (file_path.name, open(file_path, "rb"), content_type)
            }
            data = {
                "collection_name": args.collection
            }
            
            # Add metadata if provided
            if args.metadata:
                data["metadata"] = args.metadata
            
            # Send request
            response = requests.post(url, files=files, data=data)
            
            if response.status_code == 200:
                result = response.json()
                print(f"Success (ID: {result['document_id']}, Chunks: {result['chunks']})")
                success_count += 1
            else:
                print(f"Error: {response.text}")
                error_count += 1
            
            # Add a small delay to avoid overwhelming the server
            time.sleep(0.5)
            
        except Exception as e:
            print(f"Error: {str(e)}")
            error_count += 1
    
    print(f"\nImport complete: {success_count} successful, {error_count} failed")
    return 0

if __name__ == "__main__":
    sys.exit(main())
