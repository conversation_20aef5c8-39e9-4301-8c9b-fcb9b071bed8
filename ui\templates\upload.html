{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <h2 class="card-title">Upload Document</h2>
                <p class="card-text">
                    Upload a document to add to the knowledge base. Supported formats include PDF, TXT, MD, and HTML.
                </p>
                <form action="/upload" method="post" enctype="multipart/form-data" class="mt-4">
                    <div class="mb-3">
                        <label for="file" class="form-label">Select Document</label>
                        <input type="file" class="form-control" id="file" name="file" required>
                        <div class="form-text">Maximum file size: 10MB</div>
                    </div>
                    <div class="mb-3">
                        <label for="collection" class="form-label">Collection</label>
                        <input type="text" class="form-control" id="collection" name="collection" value="documents">
                        <div class="form-text">The collection to add the document to</div>
                    </div>
                    <button type="submit" class="btn btn-primary">Upload</button>
                </form>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h3 class="card-title">Supported Formats</h3>
                <ul class="list-group list-group-flush">
                    <li class="list-group-item">PDF (.pdf)</li>
                    <li class="list-group-item">Text (.txt)</li>
                    <li class="list-group-item">Markdown (.md)</li>
                    <li class="list-group-item">HTML (.html, .htm)</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
