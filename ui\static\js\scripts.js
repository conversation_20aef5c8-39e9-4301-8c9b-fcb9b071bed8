// Main JavaScript for Knowledge Base UI

document.addEventListener('DOMContentLoaded', function() {
    // Highlight the current nav item
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href === currentPath || 
            (href !== '/' && currentPath.startsWith(href))) {
            link.classList.add('active');
        }
    });
    
    // File upload validation
    const fileInput = document.getElementById('file');
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            const maxSize = 10 * 1024 * 1024; // 10MB
            if (this.files[0] && this.files[0].size > maxSize) {
                alert('File is too large. Maximum size is 10MB.');
                this.value = '';
            }
        });
    }
});
