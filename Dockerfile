FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    build-essential \
    libpoppler-cpp-dev \
    pkg-config \
    python3-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY app/ ./app/
COPY ui/ ./ui/

# Create necessary directories
RUN mkdir -p data/chroma uploads

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV CHROMA_DB_PATH=/app/data/chroma
ENV UPLOAD_DIR=/app/uploads
ENV API_PORT=8000
ENV UI_PORT=8001

# Expose ports
EXPOSE 8000
EXPOSE 8001

# Run the application
CMD ["python", "-m", "app.main"]
