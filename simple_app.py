"""
Simple Knowledge Base application using Flask.
"""

import os
import json
import uuid
import shutil
from pathlib import Path
from flask import Flask, request, render_template, redirect, url_for, jsonify, send_from_directory
from werkzeug.utils import secure_filename
import pypdf

# Create app
app = Flask(__name__, 
            template_folder=os.path.join('ui', 'templates'),
            static_folder=os.path.join('ui', 'static'))

# Create upload directory if it doesn't exist
UPLOAD_DIR = os.path.join(os.getcwd(), 'uploads')
os.makedirs(UPLOAD_DIR, exist_ok=True)

# Create data directory if it doesn't exist
DATA_DIR = os.path.join(os.getcwd(), 'data')
os.makedirs(DATA_DIR, exist_ok=True)

# Simple in-memory document store
documents = {}
document_content = {}

# Load documents from disk if available
def load_documents_from_disk():
    """Load documents metadata and content from disk."""
    global documents, document_content
    
    try:
        # Load metadata
        metadata_path = os.path.join(DATA_DIR, "documents.json")
        if os.path.exists(metadata_path):
            with open(metadata_path, "r") as f:
                documents = json.load(f)
            
        # Load content
        content_path = os.path.join(DATA_DIR, "content.json")
        if os.path.exists(content_path):
            with open(content_path, "r") as f:
                document_content = json.load(f)
                
        print(f"Loaded {len(documents)} documents from disk")
    except Exception as e:
        print(f"Error loading documents from disk: {e}")
        # Initialize empty if loading fails
        documents = {}
        document_content = {}

# Save documents to disk
def save_documents_to_disk():
    """Save documents metadata and content to disk for persistence."""
    try:
        # Save metadata
        with open(os.path.join(DATA_DIR, "documents.json"), "w") as f:
            json.dump(documents, f)
        
        # Save content
        with open(os.path.join(DATA_DIR, "content.json"), "w") as f:
            json.dump(document_content, f)
            
        print("Documents saved to disk")
    except Exception as e:
        print(f"Error saving documents to disk: {e}")

# Extract text from PDF
def extract_text_from_pdf(file_path):
    """Extract text from a PDF file."""
    try:
        text = ""
        with open(file_path, "rb") as f:
            pdf = pypdf.PdfReader(f)
            for page_num in range(len(pdf.pages)):
                page = pdf.pages[page_num]
                text += page.extract_text() + f"\n\n--- Page {page_num + 1} ---\n\n"
        return text
    except Exception as e:
        print(f"Error extracting text from PDF: {e}")
        return f"Error extracting text: {str(e)}"

# Simple search function
def simple_search(query, collection_name, n_results):
    """
    Simple search function that looks for query terms in document content.
    This is a very basic implementation without vector embeddings.
    """
    query = query.lower()
    results = []
    
    # Filter documents by collection
    collection_docs = {
        doc_id: doc for doc_id, doc in documents.items()
        if doc.get("collection") == collection_name
    }
    
    # Search for query in each document
    for doc_id, doc in collection_docs.items():
        if doc_id in document_content:
            content = document_content[doc_id].lower()
            
            # Simple relevance score based on number of query term occurrences
            relevance = content.count(query)
            
            if relevance > 0:
                # Find a snippet containing the query
                start_idx = content.find(query)
                if start_idx >= 0:
                    # Extract a snippet around the query
                    start = max(0, start_idx - 100)
                    end = min(len(content), start_idx + len(query) + 100)
                    snippet = content[start:end]
                    
                    results.append({
                        "id": doc_id,
                        "text": snippet,
                        "metadata": doc,
                        "distance": 1.0 - (relevance / 100),  # Lower is better
                    })
    
    # Sort by relevance (distance)
    results.sort(key=lambda x: x["distance"])
    
    # Return top N results
    return results[:n_results]

# Load documents on startup
load_documents_from_disk()

# Routes
@app.route('/')
def index():
    """Home page."""
    return render_template('index.html', title="Knowledge Base")

@app.route('/health')
def health():
    """Health check endpoint."""
    return jsonify({
        "status": "healthy",
        "document_count": str(len(documents)),
    })

@app.route('/upload', methods=['GET', 'POST'])
def upload():
    """Upload document page and handler."""
    if request.method == 'POST':
        # Check if a file was uploaded
        if 'file' not in request.files:
            return render_template('error.html', title="Upload Error", error="No file part")
        
        file = request.files['file']
        
        # Check if a file was selected
        if file.filename == '':
            return render_template('error.html', title="Upload Error", error="No file selected")
        
        # Generate a unique ID for the document
        document_id = str(uuid.uuid4())
        
        # Get collection name
        collection_name = request.form.get('collection', 'documents')
        
        # Create metadata
        meta_data = {
            "filename": file.filename,
            "content_type": file.content_type,
            "document_id": document_id,
            "collection": collection_name,
        }
        
        # Save the file
        filename = secure_filename(file.filename)
        file_path = os.path.join(UPLOAD_DIR, f"{document_id}_{filename}")
        file.save(file_path)
        
        # Extract text from the file based on its type
        content = ""
        if filename.lower().endswith(".pdf"):
            content = extract_text_from_pdf(file_path)
        else:
            # For other file types, just read as text
            with open(file_path, "rb") as f:
                content = f.read().decode("utf-8", errors="ignore")
        
        # Store the document metadata and content
        documents[document_id] = meta_data
        document_content[document_id] = content
        
        # Save documents to disk for persistence
        save_documents_to_disk()
        
        print(f"Added document '{filename}' with ID {document_id}")
        
        return redirect(url_for('list_documents'))
    
    # GET request - show upload form
    return render_template('upload.html', title="Upload Document")

@app.route('/documents')
def list_documents():
    """List documents page."""
    return render_template('documents.html', title="Documents", documents=list(documents.values()))

@app.route('/documents/<document_id>/delete', methods=['POST'])
def delete_document(document_id):
    """Delete a document."""
    try:
        if document_id in documents:
            # Delete the document file
            filename = documents[document_id].get("filename", "")
            file_path = os.path.join(UPLOAD_DIR, f"{document_id}_{filename}")
            if os.path.exists(file_path):
                os.remove(file_path)
            
            # Remove from memory
            del documents[document_id]
            if document_id in document_content:
                del document_content[document_id]
            
            # Save changes to disk
            save_documents_to_disk()
            
            print(f"Deleted document with ID {document_id}")
        
        return redirect(url_for('list_documents'))
    except Exception as e:
        return render_template('error.html', title="Delete Error", error=str(e))

@app.route('/search', methods=['GET', 'POST'])
def search():
    """Search page and handler."""
    if request.method == 'POST':
        query = request.form.get('query', '')
        collection = request.form.get('collection', 'documents')
        n_results = int(request.form.get('n_results', 5))
        
        results = simple_search(query, collection, n_results)
        
        return render_template('search_results.html', title="Search Results", query=query, results=results)
    
    # GET request - show search form
    return render_template('search.html', title="Search Knowledge Base")

@app.route('/api/v1/documents')
def api_list_documents():
    """API endpoint to list documents."""
    collection_name = request.args.get('collection_name', 'documents')
    limit = int(request.args.get('limit', 100))
    offset = int(request.args.get('offset', 0))
    
    # Filter by collection if specified
    filtered_docs = [
        doc for doc in documents.values()
        if doc.get("collection") == collection_name
    ]
    
    # Apply pagination
    paginated_docs = filtered_docs[offset:offset+limit]
    
    return jsonify({
        "status": "success",
        "count": len(paginated_docs),
        "documents": paginated_docs,
    })

@app.route('/api/v1/documents/upload', methods=['POST'])
def api_upload_document():
    """API endpoint to upload a document."""
    try:
        # Check if a file was uploaded
        if 'file' not in request.files:
            return jsonify({"status": "error", "message": "No file part"}), 400
        
        file = request.files['file']
        
        # Check if a file was selected
        if file.filename == '':
            return jsonify({"status": "error", "message": "No file selected"}), 400
        
        # Generate a unique ID for the document
        document_id = str(uuid.uuid4())
        
        # Get collection name
        collection_name = request.form.get('collection_name', 'documents')
        
        # Create metadata
        meta_data = {
            "filename": file.filename,
            "content_type": file.content_type,
            "document_id": document_id,
            "collection": collection_name,
        }
        
        # Save the file
        filename = secure_filename(file.filename)
        file_path = os.path.join(UPLOAD_DIR, f"{document_id}_{filename}")
        file.save(file_path)
        
        # Extract text from the file based on its type
        content = ""
        if filename.lower().endswith(".pdf"):
            content = extract_text_from_pdf(file_path)
        else:
            # For other file types, just read as text
            with open(file_path, "rb") as f:
                content = f.read().decode("utf-8", errors="ignore")
        
        # Store the document metadata and content
        documents[document_id] = meta_data
        document_content[document_id] = content
        
        # Save documents to disk for persistence
        save_documents_to_disk()
        
        print(f"Added document '{filename}' with ID {document_id}")
        
        return jsonify({
            "status": "success",
            "message": f"Document '{file.filename}' processed successfully",
            "document_id": document_id,
        })
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/api/v1/documents/<document_id>', methods=['DELETE'])
def api_delete_document(document_id):
    """API endpoint to delete a document."""
    try:
        if document_id in documents:
            # Delete the document file
            filename = documents[document_id].get("filename", "")
            file_path = os.path.join(UPLOAD_DIR, f"{document_id}_{filename}")
            if os.path.exists(file_path):
                os.remove(file_path)
            
            # Remove from memory
            del documents[document_id]
            if document_id in document_content:
                del document_content[document_id]
            
            # Save changes to disk
            save_documents_to_disk()
            
            print(f"Deleted document with ID {document_id}")
            
            return jsonify({
                "status": "success",
                "message": f"Document '{document_id}' deleted successfully",
            })
        else:
            return jsonify({"status": "error", "message": f"Document '{document_id}' not found"}), 404
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/api/v1/query', methods=['POST'])
def api_query():
    """API endpoint to query the knowledge base."""
    try:
        query = request.form.get('query', '')
        collection_name = request.form.get('collection_name', 'documents')
        n_results = int(request.form.get('n_results', 5))
        
        results = simple_search(query, collection_name, n_results)
        
        return jsonify({
            "status": "success",
            "query": query,
            "results": results,
        })
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8020, debug=True)
