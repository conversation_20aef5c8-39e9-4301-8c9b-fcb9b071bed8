{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <a href="/upload" class="btn btn-primary">Upload New Document</a>
    </div>
    <div class="col-md-6 text-end">
        <span class="badge bg-secondary">{{ documents|length }} documents</span>
    </div>
</div>

{% if documents %}
<div class="table-responsive">
    <table class="table table-striped table-hover">
        <thead>
            <tr>
                <th>Document ID</th>
                <th>Filename</th>
                <th>Type</th>
                <th>Chunks</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for doc in documents %}
            <tr>
                <td>{{ doc.document_id }}</td>
                <td>{{ doc.filename }}</td>
                <td>{{ doc.content_type }}</td>
                <td>{{ doc.chunk_count }}</td>
                <td>
                    <form action="/documents/{{ doc.document_id }}/delete" method="post" class="d-inline">
                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this document?')">Delete</button>
                    </form>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
<div class="alert alert-info">
    No documents found. <a href="/upload">Upload a document</a> to get started.
</div>
{% endif %}
{% endblock %}
