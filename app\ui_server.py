"""
UI server module for the Knowledge Base service.
"""

import os
import async<PERSON>
from pathlib import Path
from fastapi import FastAP<PERSON>, Request, Form, UploadFile, File
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jin<PERSON>2Templates
import uvicorn
from loguru import logger
import httpx

# Initialize FastAPI app for UI
ui_app = FastAPI(
    title="Knowledge Base UI",
    description="Web interface for the Knowledge Base",
)

# Set up templates
templates_dir = Path(__file__).parent.parent / "ui" / "templates"
templates_dir.mkdir(parents=True, exist_ok=True)
templates = Jinja2Templates(directory=str(templates_dir))

# Set up static files
static_dir = Path(__file__).parent.parent / "ui" / "static"
static_dir.mkdir(parents=True, exist_ok=True)
ui_app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")

# API client configuration
API_HOST = os.getenv("API_HOST", "localhost")
API_PORT = int(os.getenv("API_PORT", 8000))
API_URL = f"http://{API_HOST}:{API_PORT}"

@ui_app.get("/", response_class=HTMLResponse)
async def index(request: Request):
    """Render the index page."""
    return templates.TemplateResponse(
        "index.html",
        {"request": request, "title": "Knowledge Base"}
    )

@ui_app.get("/upload", response_class=HTMLResponse)
async def upload_page(request: Request):
    """Render the upload page."""
    return templates.TemplateResponse(
        "upload.html",
        {"request": request, "title": "Upload Document"}
    )

@ui_app.post("/upload")
async def upload_document(
    request: Request,
    file: UploadFile = File(...),
    collection: str = Form("documents"),
):
    """Handle document upload."""
    try:
        # Forward the request to the API
        async with httpx.AsyncClient() as client:
            form_data = {"collection_name": collection}
            files = {"file": (file.filename, await file.read(), file.content_type)}
            
            response = await client.post(
                f"{API_URL}/api/v1/documents/upload",
                data=form_data,
                files=files,
            )
            
            if response.status_code == 200:
                return RedirectResponse(url="/documents", status_code=303)
            else:
                return templates.TemplateResponse(
                    "error.html",
                    {
                        "request": request,
                        "title": "Upload Error",
                        "error": f"Error uploading document: {response.text}",
                    }
                )
    except Exception as e:
        logger.error(f"Error uploading document: {e}")
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "title": "Upload Error",
                "error": str(e),
            }
        )

@ui_app.get("/documents", response_class=HTMLResponse)
async def list_documents(request: Request):
    """Render the documents page."""
    try:
        # Get documents from the API
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{API_URL}/api/v1/documents")
            
            if response.status_code == 200:
                documents = response.json().get("documents", [])
                return templates.TemplateResponse(
                    "documents.html",
                    {
                        "request": request,
                        "title": "Documents",
                        "documents": documents,
                    }
                )
            else:
                return templates.TemplateResponse(
                    "error.html",
                    {
                        "request": request,
                        "title": "Error",
                        "error": f"Error fetching documents: {response.text}",
                    }
                )
    except Exception as e:
        logger.error(f"Error fetching documents: {e}")
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "title": "Error",
                "error": str(e),
            }
        )

@ui_app.get("/search", response_class=HTMLResponse)
async def search_page(request: Request):
    """Render the search page."""
    return templates.TemplateResponse(
        "search.html",
        {"request": request, "title": "Search Knowledge Base"}
    )

@ui_app.post("/search", response_class=HTMLResponse)
async def search(
    request: Request,
    query: str = Form(...),
    collection: str = Form("documents"),
    n_results: int = Form(5),
):
    """Handle search request."""
    try:
        # Forward the request to the API
        async with httpx.AsyncClient() as client:
            form_data = {
                "query": query,
                "collection_name": collection,
                "n_results": n_results,
            }
            
            response = await client.post(
                f"{API_URL}/api/v1/query",
                data=form_data,
            )
            
            if response.status_code == 200:
                results = response.json().get("results", [])
                return templates.TemplateResponse(
                    "search_results.html",
                    {
                        "request": request,
                        "title": "Search Results",
                        "query": query,
                        "results": results,
                    }
                )
            else:
                return templates.TemplateResponse(
                    "error.html",
                    {
                        "request": request,
                        "title": "Search Error",
                        "error": f"Error searching: {response.text}",
                    }
                )
    except Exception as e:
        logger.error(f"Error searching: {e}")
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "title": "Search Error",
                "error": str(e),
            }
        )

@ui_app.post("/documents/{document_id}/delete")
async def delete_document(request: Request, document_id: str):
    """Handle document deletion."""
    try:
        # Forward the request to the API
        async with httpx.AsyncClient() as client:
            response = await client.delete(
                f"{API_URL}/api/v1/documents/{document_id}",
            )
            
            if response.status_code == 200:
                return RedirectResponse(url="/documents", status_code=303)
            else:
                return templates.TemplateResponse(
                    "error.html",
                    {
                        "request": request,
                        "title": "Delete Error",
                        "error": f"Error deleting document: {response.text}",
                    }
                )
    except Exception as e:
        logger.error(f"Error deleting document: {e}")
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "title": "Delete Error",
                "error": str(e),
            }
        )

async def start_ui_server(host: str = "0.0.0.0", port: int = 8001):
    """Start the UI server."""
    config = uvicorn.Config(
        "app.ui_server:ui_app",
        host=host,
        port=port,
        log_level="info",
    )
    server = uvicorn.Server(config)
    await server.serve()
